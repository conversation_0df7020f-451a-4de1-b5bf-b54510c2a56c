import { QueryContext, BuilderModule } from './types';

export class ThreadSynchronizationBuilder implements BuilderModule {
  build(context: QueryContext): string {
    return `
# 🧵 双线程数据同步机制完整指南

## 双线程模型基本原理
抖音小程序采用双线程架构，逻辑层和渲染层分离，这种架构在提升性能的同时也带来了数据同步的挑战。

- **逻辑层（JS线程）**: 运行JavaScript代码，处理业务逻辑，调用小程序API
- **渲染层（Render线程）**: 负责页面渲染，基于TTML/TTSS构建UI
- **通信方式**: 两层通过异步消息传递机制通信，而非直接共享内存
- **数据流向**: 数据从逻辑层通过setData方法传递到渲染层，是单向流动的

## 数据同步核心机制

### setData异步机制
- setData是异步操作，不会立即更新渲染层的数据
- 数据传递有序列化开销，大数据量传递会影响性能
- 渲染层接收到数据后才会触发页面重新渲染

### 数据传递最佳实践
- **批量更新**: 避免频繁调用setData，尽量批量更新数据
- **增量更新**: 只传递变化的数据，而非整个对象
- **路径更新**: 使用点记法更新嵌套属性，如 'user.name': 'newName'

## 数据初始化规范

### 初始数据结构要求
- data属性必须设置完整的初始结构
- 所有属性都应有默认值，避免undefined或null
- 嵌套对象和数组都应预设结构

### 正确初始化示例：
\\\`\\\`\\\`javascript
Card({
  data: {
    userInfo: {
      name: '',
      avatar: '',
      id: 0
    },
    listData: [],
    status: {
      loading: false,
      error: null,
      success: false
    },
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0
    }
  }
});
\\\`\\\`\\\`

### 错误初始化示例：
\\\`\\\`\\\`javascript
Card({
  data: {
    userInfo: null,    // ❌ 错误：应该预设结构
    listData: undefined, // ❌ 错误：应该设置为空数组
    status: {}         // ❌ 错误：应该预设所有状态字段
  }
});
\\\`\\\`\\\`

## 同步时序保证

### setData回调机制
使用setData的回调函数确保数据已传递到渲染层：

\\\`\\\`\\\`javascript
this.setData({
  loading: true,
  data: newData
}, () => {
  // 数据已同步到渲染层，可以安全执行依赖UI的操作
  console.log('数据同步完成');
  this.performUIOperation();
});
\\\`\\\`\\\`

### 定时器确保同步
在某些复杂场景下使用setTimeout确保UI线程同步：

\\\`\\\`\\\`javascript
this.setData({ loading: true });
setTimeout(() => {
  // 确保UI已更新
  this.processNextStep();
}, 0);
\\\`\\\`\\\`

### 依赖执行顺序
在回调中执行依赖于UI更新的逻辑：

\\\`\\\`\\\`javascript
// 正确做法
this.setData({ 
  showModal: true 
}, () => {
  // 确保模态框已渲染后再执行动画
  this.animateModal();
});

// 错误做法
this.setData({ showModal: true });
this.animateModal(); // ❌ 可能在UI未更新时执行
\\\`\\\`\\\`

## 数据同步性能优化

### 避免频繁setData
批量更新替代多次单独更新：

\\\`\\\`\\\`javascript
// ❌ 错误：频繁调用setData
this.setData({ name: 'John' });
this.setData({ age: 25 });
this.setData({ city: 'Beijing' });

// ✅ 正确：批量更新
this.setData({
  name: 'John',
  age: 25,
  city: 'Beijing'
});
\\\`\\\`\\\`

### 数据差异检测
在setData前检查数据是否真正发生变化：

\\\`\\\`\\\`javascript
updateUserInfo(newInfo) {
  if (JSON.stringify(this.data.userInfo) !== JSON.stringify(newInfo)) {
    this.setData({ userInfo: newInfo });
  }
}
\\\`\\\`\\\`

### 大数据量处理策略
分片传递、虚拟列表处理：

\\\`\\\`\\\`javascript
// 分片传递大数据
const CHUNK_SIZE = 50;
const chunks = [];
for (let i = 0; i < largeData.length; i += CHUNK_SIZE) {
  chunks.push(largeData.slice(i, i + CHUNK_SIZE));
}

chunks.forEach((chunk, index) => {
  setTimeout(() => {
    this.setData({
      [\`listData[\${index * CHUNK_SIZE}]\`]: chunk
    });
  }, index * 10);
});
\\\`\\\`\\\`

## 常见同步问题和解决方案

### 数据更新后立即访问DOM失败
使用setData回调或nextTick：

\\\`\\\`\\\`javascript
// 解决方案1：使用回调
this.setData({ itemHeight: 100 }, () => {
  const query = lynx.createSelectorQuery();
  query.select('.item').boundingClientRect();
  query.exec((res) => {
    console.log('元素高度:', res[0].height);
  });
});

// 解决方案2：使用nextTick
this.setData({ itemHeight: 100 });
lynx.nextTick(() => {
  // 在下一个渲染周期执行
  this.measureElement();
});
\\\`\\\`\\\`

### 连续快速更新导致数据丢失
合并更新或使用队列机制：

\\\`\\\`\\\`javascript
// 防抖合并更新
let updateTimer = null;
let pendingUpdates = {};

function updateDataDebounced(data) {
  Object.assign(pendingUpdates, data);
  
  if (updateTimer) {
    clearTimeout(updateTimer);
  }
  
  updateTimer = setTimeout(() => {
    this.setData(pendingUpdates);
    pendingUpdates = {};
    updateTimer = null;
  }, 16); // 一帧的时间
}
\\\`\\\`\\\`

### 大数据量传递导致页面卡顿
数据分片、增量更新、虚拟化处理：

\\\`\\\`\\\`javascript
// 虚拟化处理
handleVirtualList(scrollTop) {
  const itemHeight = 100;
  const visibleCount = Math.ceil(this.data.screenHeight / itemHeight);
  const startIndex = Math.floor(scrollTop / itemHeight);
  const endIndex = startIndex + visibleCount;
  
  const visibleItems = this.data.allItems.slice(startIndex, endIndex);
  
  this.setData({
    visibleItems,
    scrollViewHeight: this.data.allItems.length * itemHeight,
    translateY: startIndex * itemHeight
  });
}
\\\`\\\`\\\`

## 高级同步技巧

### 自定义同步状态管理
创建专门的数据同步管理器：

\\\`\\\`\\\`javascript
class DataSyncManager {
  constructor(component) {
    this.component = component;
    this.updateQueue = [];
    this.isUpdating = false;
  }
  
  batchUpdate(data) {
    this.updateQueue.push(data);
    if (!this.isUpdating) {
      this.flushUpdates();
    }
  }
  
  flushUpdates() {
    if (this.updateQueue.length === 0) return;
    
    this.isUpdating = true;
    const mergedData = Object.assign({}, ...this.updateQueue);
    this.updateQueue = [];
    
    this.component.setData(mergedData, () => {
      this.isUpdating = false;
      this.flushUpdates(); // 处理队列中的后续更新
    });
  }
}
\\\`\\\`\\\`

### 跨线程事件通信
使用自定义事件实现跨线程通信：

\\\`\\\`\\\`javascript
// 逻辑层发送事件
this.triggerEvent('dataSync', {
  type: 'UPDATE_USER',
  payload: userData
});

// 渲染层接收事件
<view binddataSync="handleDataSync">
</view>
\\\`\\\`\\\`

## 数据同步最佳实践总结

### 初始化原则
1. 所有数据属性必须有默认值
2. 嵌套对象和数组预设完整结构
3. 避免在初始化时使用null或undefined

### 更新原则
1. 使用批量更新减少setData调用次数
2. 利用路径更新优化嵌套属性修改
3. 在回调中执行依赖UI更新的逻辑

### 性能原则
1. 大数据量使用分片传递
2. 实现数据差异检测避免无效更新
3. 使用虚拟化处理超长列表

### 错误处理原则
1. 使用try-catch包裹setData操作
2. 设置合理的超时机制
3. 提供降级方案处理同步失败

这些机制确保了Lynx框架在双线程架构下的数据一致性和性能优化，是构建高质量移动应用的关键基础。
`;
  }
}

export const threadSynchronizationBuilder = new ThreadSynchronizationBuilder();