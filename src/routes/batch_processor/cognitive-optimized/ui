UX
@李泽宇 
相关文档
AI搜2.0-Demo Case
画布标准中的视觉部分
AI画布测试集数据
画布pc模拟：https://ai-search-canvas-project.gf.bytedance.net/
figma
https://www.figma.com/design/PFbFhanDrCeaNHQ7DDEIrP/%E6%99%BA%E8%83%BD%E7%94%BB%E5%B8%83%E8%A7%84%E8%8C%83%E5%BB%BA%E7%AB%8B%E8%8D%89%E7%A8%BF?node-id=95-8218&t=exTcXBdnTIbKT8LS-4
一句话背景，从设计体验优化智能画布的呈现效果，具有抖音特性，提升画布生成美观度及消费体验；
当前模型选择现状
query示例“霍去病家族关系”

问题：美观度差，可读性低； 文本层级关系不清晰；整体风格与抖音差距较大；




claude
[图片]
豆包模型
[图片]

GPT4.0
[图片]

gemini
[图片]

GPT4.1
[图片]



智能画布应用场景
场景一
ai搜bot、综搜首位区
query生成画布
场景二
视频转画布
视频解析成画布
场景三
综搜智能如意
query生成画布
[图片]

[图片]

[图片]


目标
目标
手段
举例
惊喜感
区别markdown，画布内容更图形化，更像小红书文字图片

同一内容的多结构表达

结构化文本的多种结构表达
形态一
[图片]

形态二
[图片]

形态三
[图片]

体验一致
画布在多场景的应用中，阅读效率、展现效率更一致
结构与布局的统一

- ai搜场景、视频转画布场景、智能如意场景下的体验是一致的
- 移动端的体验更符合移动端交互行为


设计解决思路与进展
通过拆分长尾垂类内容，将内容以通用性，个性化两种维度进行呈现，提升可阅读性；
通用类型：问答类型，文本结构化；
个性化类型：长尾类垂类，生活决策等；
暂时无法在飞书文档外展示此内容

阶段
手段
todo
阶段一
及格线，防止粗暴样式上线

设计给“页面展示原则”prompt
运营依据原则输出与打分

[x] 设计提供基础“页面展示原则，@刘宁 

阶段二
将0、1、2分率样式提高至3分率

设计提供详细“美观度标准”
人工标注打分，告诉模型什么是goodcase/badcase
需完成
- 典型case收集与遍历
- 典型case设计
- 通用视觉规则提炼
- 人工打分规则提炼

[x] 设计8个通用case的效果图@尹尧 @李泽宇 
[x] 5.14日组内对齐下效果图和预期@李泽宇 
[x] 5.16 与业务对齐效果图和草稿标准@尹尧 @李泽宇 
[] 撰写视觉标准，持续进行中
[x] 5.20 给pm提供打分标准与case
[x] 5.23 视频转图文case设计ai总结-文本case for设计
[] 
阶段三
将3分率样式提高至5分率

设计提供行业组件，（eg.拓扑图、表格、脉络图等）。
1、将figma组件转化为html，喂给模型，模型学习（适合在线）
2、前段开发组件容器，灌入模型生成数据，约束样式展现（适合离线）
需完成
- 典型组件case搜集
- 典型组件设计
- 组件规则提炼
[x] 收集常用行业组件
  - 截止520，高优：文本、时间轴、表格
[x] 5.21v文本组件模板设计
[x] 5.21v时间轴模板设计，进行中
[x] 5.23表格模板设计
[] 图片模板设计



视觉设计标准根据以下五个维度确立
暂时无法在飞书文档外展示此内容

1. 阶段一-运营评估标准 Done
for评估测评用，粗颗粒度

原则
信息结构

- 结构布局符合内容逻辑
- 清晰简单符合普世理解
- 信息从主到次关系清晰
文字排版/样式

- 字体不超过3款
- 字体大小不超过5个
- 字体不可加阴影、描边、立体、渐变样式
- 主标题可用彩色，其余禁用（标签、图表可用）
空间关系
- 内容整体左对齐，不允许剧中、左对齐2种版式
- 卡片边距20，内边距16
- 文本行间距不超过字号数
- 卡片内区块间距20
- 模块卡片内不可再叠加2个以上区块
图形
- 图标实色填充不可用描边
- 图标风格圆润不可用尖锐的转角
- 图标、表格图形使用扁平风格
- 可用纯色、渐变不可加立体质感
色彩
- 文字颜色不超过3种
- 不可大面积用底色
- 画布色彩遵循低饱和度、高明度

2. 阶段二-精细化UI品质感 -ing

2.1 典型case重新设计
Case
手动刷两个场景的case
- Ai搜智能画布：文档内前8个case视觉效果优化PE_canvas_20250416
- 综搜视频转画布：3个case的设计ai总结-文本case for设计
figma

https://www.figma.com/design/PFbFhanDrCeaNHQ7DDEIrP/%E6%99%BA%E8%83%BD%E7%94%BB%E5%B8%83%E8%A7%84%E8%8C%83%E5%BB%BA%E7%AB%8B%E8%8D%89%E7%A8%BF?node-id=0-1&t=SGqJBN5H0D4HVrRw-1
参与行动的各位找@李泽宇 开个编辑权限
场景一
Ai智能画布、首位ai区
更具query生成
case1
case2-郑州上门收钱币服务机构

case3-广西贺州禁渔期通知

case4-2025年做什么生意最赚钱

case5-陌上又花开剧情简介

万象城为什么不收停车费

万能除草剂配方


[图片]

[图片]
[图片]
[图片]
[图片]

[图片]

[图片]

[图片]

[图片]

[图片]

[图片]

[图片]

[图片]















场景二
视频转画布
视频解析画布
case1
case2

case3










[图片]

[图片]


[图片]











2.2 提炼通用视觉规则

框架
隐喻

以现实世界的杂志为灵感，通过对图文进行版式设计，满足信息的可读性，同时增加内容的丰富感

- 每次展现都有惊喜感，要求结构一致的情况下，有更丰富的色彩、图形展示
- 有自己的主题，通过图形、颜色表现当前主题
适应
统一的设计语言动态化适配多设备，保持颜色、图标等核心元素一致性，仅布局和交互根据屏幕尺寸调整
- 手机端：推荐纵向布局，减少横滑等特殊交互，避免内容超出屏幕。
- pad端：避免将移动端等比例放大，需动态适配
- pc端：以鼠标为交互的方案设计，避免使用移动端交互，例如横滑、拖拽、捏合等
调性
通过高对比色彩、大小字排版和留白突出核心内容，内容轻量、整洁、干净，减少冗余元素，使用户聚焦关键信息

- 画布背景：纯白色
- 卡片背景：纯白色，无投影，不得出现卡套卡情况
- 元素背景：面积大则用浅色，避免大面积深色
- 间距留白：留白大，间距不拥挤
- 图形图标：精致，单彩色，深色
- 颜色使用：单一小卡内使用同一类型颜色，即每张卡可使用一个主题色
下图举例：共有四个小卡，每张卡都有自己的主题色
[图片]

布局
定义标题、正文、按钮等字体比例和谐，确保可读性和层次感
- 文字比例：标题最重，其次正文，再次描述性文本
- 比重设计：可通过深色、icon强调比重更大的内容
- 对齐方式：元素左对齐、居中对齐，不得出现错位情况
- 图形与图标：简洁具备可识别性，单一色彩
- 层级嵌套：不得出现卡套卡结构，层级要简单
https://bytedance.larkoffice.com/sync/Sh3YdbY1csgh4db2m84ckrIXn7f



颜色

https://bytedance.larkoffice.com/sync/HLWidUOBDsERiSbuhOScEtBqnZe
文字
https://bytedance.larkoffice.com/sync/HeUndCkY1sR2gkbqTUdcJdtSnTb

间距
https://bytedance.larkoffice.com/sync/VDk2dO7bos8vB4byWCIcp0FRnyh

圆角
https://bytedance.larkoffice.com/sync/ZdbIdlCRLszaCkbkTq1cI7CLnld

图标
https://bytedance.larkoffice.com/sync/BxtudRb6Os5JLdbGsYucYvbSnOc
描边
https://bytedance.larkoffice.com/sync/POZnd0HFNscDm5bVUTHcFyjHnDc


2.3 人工打分标准
对当前生成效果进行人工标注打分，总分3分，将低分率内容拔高至高分率。但当前高分率依然达不到设计标准。
打分标准从大到小，从宏观到微观
打分细则
打分标准
3分，美观度高，可读性强，无bug



2分，美观度一般，无错乱，无bug



1分，美观度差，版式不合理，无元素错位



0分，有bug，元素错位等


移动适配
推荐纵向布局，减少横滑等特殊交互，避免内容超出屏幕。

































调性

整体色调：P00
- 0分：颜色不轻量，颜色扎眼，彩色面积过多
- 1分：颜色轻量不扎眼，80%白色，仅部分组件标题有重色
- 3分：颜色轻量不扎眼，90%白色，无大面积重色彩色，重色只出现在icon与文字上


3分（仅看色调维度）



2分（仅看色调维度）



1分（仅看色调维度）



0分（仅看色调维度）




大面积白色，颜色轻量，重色用的少，且用在标识上
[图片]

大面积白色，重色用的少，且用在了要突出的重点内容上
[图片]

理由同左侧
[图片]


重色用错位置，应该用在icon或者标题上，卡片不要重色
[图片]

上半部分OK，下半部分颜色太扎眼
[图片]

重色面积太大，过度吸引注意力，影响内容消费
[图片]


背景不是白色，颜色太扎眼
[图片]

重色太多
[图片]

重色面积太大，过度
[图片]


颜色太扎眼，毫无重点
[图片]

背景用了彩色，要白色
[图片]

背景用了彩色，要白色
[图片]

层级关系

卡片层级 P1
0分：严重卡套卡，内容套内容
1分：内容套内容
2分：无卡套卡，无内容套内容，但卡片有投影没拍平
3分：无卡套卡，内容拍平，卡片无投影，靠内容分割

3分（仅看卡片层级维度）



2分（仅看卡片层级维度）



1分（仅看卡片层级维度）



0分（仅看卡片层级维度）




层级简单，内容突出
[图片]


[图片]


[图片]


卡片嵌套不严重
[图片]

嵌套严重，层级过多
[图片]





[图片]





嵌套严重，层级过多
[图片]



布局合理

布局合理 P0（治理中）
0分：元素错位，对齐方式错位，icon多大，和标题不协调
1分：部分元素错位，布局不合理
2分：无元素错位，但布局布局不合理，留白不合理
3分：元素对齐方式合理，icon和标题大小比例合适

3分（仅看布局维度）



2分（仅看布局级维度）



1分（仅看布局维度）



0分（仅看布局层级维度）




[图片]

[图片]



无元素错位，但左侧留白太大，布局不合理
[图片]




少部分内容布局不合理
[图片]

icon和文本对齐有问题
[图片]



元素布局错乱
[图片]

icon和标题比例失衡
[图片]

图形严重错位
[图片]


内容分组明确 P1
0分：内容聚集在一起无分割，阅读不清晰


3分：靠留白间距区隔不同模块，标题明显，能明显区分每个段落

3分（仅看分割维度）



2分（仅看分割维度）



1分（仅看分割维度）



0分（仅看分割维度）




[图片]




分割明确，但不要分割线
[图片]

分割明确，但不要卡片投影
[图片]







各个组内容糊在了一起
[图片]

内容分割混乱
[图片]

内容分割混乱
[图片]

文字
文字协调 P2
3分：对比度大，文字颜色不扎眼，标题字号17，最小字号13，描述文字不要用彩色，仅标题可用彩色

3分（仅看文字维度）



2分（仅看文字维度）



1分（仅看文字维度）



0分（仅看文字维度）




[图片]









描述文字不要用彩色，仅标题可用彩色
[图片]

标题不要用这么重的背景色
[图片]





图标

图标大小合理，尺寸合理P1
3分：icon和文字比例协调，icon不要过大，icon不要浅色背景套深色图形，不要过多使用icon，每个组只能使用在统一级别内容上

3分（仅看icon维度）



2分（仅看icon维度）



1分（仅看icon维度）



0分（仅看icon维度）




icon用在了标题前，icon和文字比例和谐
[图片]




icon使用很合理，但icon不要浅色背景套深色图形
[图片]




icon不要浅色背景套深色图形
[图片]




标题和内容都使用了icon，不要浅色背景套深色图形
[图片]

同左侧
[图片]

同左侧
[图片]
[图片]

高亮色块
色块用在描述文字上，轻量合理 P0
3分：色块要用在描述文字上，不要给标题加色块。卡片不要前面彩色竖线。色块颜色轻量柔和不扎眼。每个组都有自己的颜色，真题色调丰富

3分（仅看色块维度）



2分（仅看色块维度）



1分（仅看色块维度）



0分（仅看高块维度）




颜色柔和，突出有价值信息
[图片]

[图片]

[图片]


颜色柔和轻量，不扎眼，每个组都有自己的颜色
[图片]



标题不要全都是一个颜色，且颜色太蓝了不柔和
[图片]


色块用在描述文字上，不要把标题包进去
[图片]

颜色轻量，但标题都是一个蓝色
[图片]

部分色块太重，要用轻量颜色
[图片]


卡片前面不要彩色竖线
[图片]

颜色太单调，太扎眼
[图片]


0530首批治理P0问题
画布设计维度不再进行0/1/2/3打分，只对关键的设计维度做 符合/不符合 的01判断，按照优先级逐个维度治理，首批治理的高优维度如下：
1、调性P0：画布底色仅允许为白色or浅灰色，其他颜色均不允许
白色✅
浅灰色✅
浅蓝色
黄色
深蓝色
[图片]
[图片]
[图片]
[图片]
[图片]

2、色块P0：
- 头部标题 不允许使用有底色的色块样式，应修改为字体彩色/黑色、无背景色的样式
- 内容模块不允许使用如下效果：1️⃣标题反色 2️⃣内容反色  3️⃣色块前竖线 4️⃣标题被包进色块内（二级标题）5️⃣颜色过于扎眼
- 所有卡片样式都不允许使用 阴影效果，保持扁平风格
头部标题反色
内容模块标题反色
内容反色
色块前竖线
标题被包进色块内
卡片阴影-小卡
卡片阴影-全局
[图片]
[图片]

[图片]

[图片]
[图片]
[图片]
[图片]
[图片]
[图片]
[图片]

[图片]
[图片]
[图片]
[图片]

[图片]

色值检测







修复预期







3. 阶段三-精细化行业组件UI标准 -ing
3.1 收集常用行业组件
拓扑图

[图片]








表格

[图片]

[图片]

[图片]

[图片]
[图片]

[图片]

[图片]

[图片]

[图片]

图表
[图片]

[图片]

[图片]

[图片]

[图片]

[图片]

[图片]


流程图
[图片]

[图片]

[图片]

[图片]

[图片]

[图片]



宫格
[图片]

[图片]

[图片]

[图片]

[图片]

[图片]

[图片]

[图片]

对比
[图片]

[图片]

[图片]

[图片]

[图片]




tips文本展示
[图片]

[图片]

[图片]

[图片]





横轴
[图片]

[图片]

[图片]

[图片]






[图片]








父子集
[图片]








公式
[图片]

[图片]








3.2 行业组件精细化设计
执行思路：结合设计好的good组件转html代码，让模型持续学习（包含布局、间距、字号等）
文本
list结构

[图片]

宫格结构
[图片]
[图片]

上下结构

[图片]

tips
[图片]

figma
https://www.figma.com/design/PFbFhanDrCeaNHQ7DDEIrP/%E6%99%BA%E8%83%BD%E7%94%BB%E5%B8%83%E8%A7%84%E8%8C%83%E5%BB%BA%E7%AB%8B%E8%8D%89%E7%A8%BF?node-id=95-8218&t=exTcXBdnTIbKT8LS-4
时间轴
时间轴

[图片]
figma
https://www.figma.com/design/PFbFhanDrCeaNHQ7DDEIrP/%E6%99%BA%E8%83%BD%E7%94%BB%E5%B8%83%E8%A7%84%E8%8C%83%E5%BB%BA%E7%AB%8B%E8%8D%89%E7%A8%BF?node-id=74-5220&t=65lSvWbrvCzLvRh6-4

表格
彩色表格 
用于page内有多表格，且表格内容不多

[图片]

- 表头背景
  - 颜色：彩色色卡-D1档
- 表头标题
  - 字号：建议14加粗
  - 颜色：白色
- 表格背景
  - 颜色：彩色色卡L1、L2交替
- 表格内容
  - 字号：建议13，单元格内左对齐
  - 颜色：黑色色卡-D2档
白色表格
用于page内有较少表格，且表格内容巨多
[图片]

- 表头背景
  - 颜色：彩色色卡-L1档
- 表头标题
  - 字号：建议13
  - 颜色：彩色色卡-D1档
- 表格背景
  - 颜色：白色
- 表格内容
  - 字号：建议13，单元格内左对齐
  - 颜色：黑色色卡-D2档
- 表格宽度适配
[图片]
- 表格栅格
[图片]

设计稿
figma设计稿↗︎
以上提到的色卡：色卡链接↗︎
图片
